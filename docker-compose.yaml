services:
  codeocr:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: codeocr
    expose:
      - "8000"
    ports:
      - "3000:3000"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - OPENAI_API_BASE=${OPENAI_API_BASE:-}
      - MODEL_NAME=${MODEL_NAME:-}
      - BACKEND_URL=http://codeocr:8000
      - NODE_ENV=${NODE_ENV:-production}
    restart: unless-stopped
